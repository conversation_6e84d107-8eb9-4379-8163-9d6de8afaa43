package com.prospection.refdata.common

import org.apache.spark.SparkContext
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.io.File

/**
 * A utility class that redirects Spark artifact paths from read-only locations to writable locations.
 * This is necessary because Spark 4.0 sometimes attempts to create directories in /app/artifacts
 * even when configuration specifies other locations.
 */
object SparkArtifactPathRedirector {
    
    // Directory where all artifact operations will be redirected if they target read-only locations
    private const val WRITABLE_ARTIFACT_DIR = "/tmp/spark-artifacts"
    
    // Directories that are known to be read-only in the container environment
    private val READ_ONLY_PATHS = setOf("/app/artifacts", "/app")
    
    /**
     * Initialize the path redirector by hooking into JVM file operations
     */
    fun initialize() {
        // Make sure our writable directory exists
        createWritableDirectory()
        
        // Set system properties to help redirect artifact operations
        System.setProperty("spark.sql.artifact.dir", WRITABLE_ARTIFACT_DIR)
        System.setProperty("spark.sql.artifact.root.dir", WRITABLE_ARTIFACT_DIR)
        System.setProperty("java.io.tmpdir", "/tmp")
        
        // Log that we've initialized
        println("SparkArtifactPathRedirector initialized, redirecting artifact paths from read-only locations to $WRITABLE_ARTIFACT_DIR")
    }
    
    /**
     * Creates the writable directory to store artifacts
     */
    private fun createWritableDirectory() {
        val dir = File(WRITABLE_ARTIFACT_DIR)
        if (!dir.exists()) {
            try {
                dir.mkdirs()
                println("Created writable artifact directory at $WRITABLE_ARTIFACT_DIR")
            } catch (e: Exception) {
                System.err.println("Failed to create writable artifact directory: ${e.message}")
            }
        }
    }
    
    /**
     * Helper method to determine if a path is inside a read-only directory
     */
    fun isReadOnlyPath(path: Path): Boolean {
        val pathStr = path.toString()
        return READ_ONLY_PATHS.any { readOnlyPath ->
            pathStr.startsWith(readOnlyPath)
        }
    }
    
    /**
     * Redirect a path from a read-only location to our writable directory.
     * Returns the original path if it's not in a read-only location.
     */
    fun redirectPath(path: Path): Path {
        if (!isReadOnlyPath(path)) {
            return path
        }
        
        // Get just the filename/directory name
        val fileName = path.fileName.toString()
        
        // Redirect to our writable directory
        val redirectedPath = Paths.get(WRITABLE_ARTIFACT_DIR, fileName)
        println("Redirecting path from ${path} to ${redirectedPath}")
        
        return redirectedPath
    }
}
