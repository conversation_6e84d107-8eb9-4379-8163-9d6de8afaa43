#!/bin/bash

# Test script to verify Spark artifact directory fix locally
# This script builds and runs the Docker container to test the fix

set -e

echo "=== Testing Spark Artifact Directory Fix ==="

# Build the Docker image
echo "Building Docker image..."
docker build . \
    --tag pd-ref-data-service:test-artifact-fix \
    --build-arg NEXUS_USER="${NEXUS_USER}" \
    --build-arg NEXUS_PASSWORD="${NEXUS_PASSWORD}" \
    --target app

# Create a test container with read-only filesystem (similar to ECS)
echo "Creating test container with read-only filesystem..."
docker run --rm -d \
    --name spark-artifact-test \
    --read-only \
    --tmpfs /tmp:rw,noexec,nosuid,size=1g \
    -e SPRING_PROFILES_ACTIVE=test \
    -e SPRING_DATA_SOURCE_SERVER_NAME=localhost \
    -e SPRING_DATA_SOURCE_USER=test \
    -e SPRING_DATA_SOURCE_DATABASE_NAME=test \
    -e ENV_NAME=test \
    -e S3_BUCKET=test-bucket \
    -e ETL_QUEUE_URL=http://localhost:4566/000000000000/test-queue.fifo \
    -e AWS_REGION=ap-southeast-2 \
    -e CUSTOMER_SERVICE_URL=http://localhost:11080 \
    -e DASHX_SERVICE_URL=http://localhost:8080 \
    -e APPLICATION_NAME=test-pd-ref-data-service \
    -e JWT_SECRET=test-secret \
    -e SNS_TOPIC_ARN=test-topic \
    -p 8080:8080 \
    pd-ref-data-service:test-artifact-fix

echo "Container started. Waiting for application to start..."
sleep 30

# Check if the application started successfully
echo "Checking application health..."
if docker logs spark-artifact-test 2>&1 | grep -q "Failed to create a temp directory.*artifacts"; then
    echo "❌ FAILED: Artifact directory error still present"
    docker logs spark-artifact-test | tail -50
    docker stop spark-artifact-test
    exit 1
elif docker logs spark-artifact-test 2>&1 | grep -q "Started.*Application"; then
    echo "✅ SUCCESS: Application started without artifact directory errors"
    docker logs spark-artifact-test | grep -E "(Started|artifact|temp directory)" | tail -10
else
    echo "⚠️  UNKNOWN: Application may still be starting or other errors present"
    docker logs spark-artifact-test | tail -20
fi

# Test a simple Spark operation if possible
echo "Testing basic application endpoint..."
if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo "✅ SUCCESS: Application health endpoint responding"
else
    echo "⚠️  WARNING: Health endpoint not responding (may be expected in test environment)"
fi

# Cleanup
echo "Cleaning up test container..."
docker stop spark-artifact-test

echo "=== Test completed ==="
